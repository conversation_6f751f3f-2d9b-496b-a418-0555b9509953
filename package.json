{"name": "image-tauri", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-http": "~2.4.4", "@tauri-apps/plugin-opener": "^2.2.7", "pinia": "^3.0.3", "vue": "^3.5.16"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}}