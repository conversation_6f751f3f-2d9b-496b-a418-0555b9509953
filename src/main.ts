import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import Notification from "@/components/Notification.vue";
import type { NotificationInstance } from "./types/notification";

import "@/assets/index.css";

const pinia = createPinia();
const app = createApp(App);

// 创建并挂载通知组件
const notification = createApp(Notification);
const notificationEl = document.createElement('div');
document.body.appendChild(notificationEl);
const notificationInstance = notification.mount(notificationEl) as NotificationInstance;

// 注册全局函数
app.config.globalProperties.$showStatus = (
  message: string,
  type: 'success' | 'warn' | 'error'
) => {
  notificationInstance.showNotification(message, type);
};

app.use(pinia);
app.mount("#app");
