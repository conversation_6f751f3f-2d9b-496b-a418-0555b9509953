<template>
  <div class="relative w-screen h-screen">
    <!-- 全屏遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-10"></div>

    <div class="relative flex items-center justify-center w-full h-full z-20">
      <!-- 悬浮步骤指示器 -->
      <div class="absolute top-4 left-4 flex gap-2">
        <div
          class="w-8 h-8 rounded-full flex items-center justify-center text-white"
          :class="guideStep === 'login' ? 'bg-blue-500' : 'bg-gray-400'"
        >
          1
        </div>
        <div
          class="w-8 h-8 rounded-full flex items-center justify-center text-white"
          :class="guideStep === 'setting' ? 'bg-blue-500' : 'bg-gray-400'"
        >
          2
        </div>
      </div>

      <Login v-if="guideStep === 'login'" />
      <Setting v-if="guideStep === 'setting'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Login from "./Login.vue";
import Setting from "./Setting.vue";

type GuideStep = "login" | "setting";
const guideStep = ref<GuideStep | null>("login");
</script>
