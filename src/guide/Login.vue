<template>
  <div class="relative w-screen h-screen">
    <!-- 全屏遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-10"></div>

    <!-- 登录内容 -->
    <div
      class="relative flex flex-col items-center justify-center w-full h-full z-20"
    >
      <!-- tab标题 -->
      <div class="flex mb-8">
        <button
          @click="loginType = 'google'"
          class="px-6 py-2 rounded-l-lg transition-colors"
          :class="
            loginType === 'google'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 hover:bg-gray-300'
          "
        >
          Google Login
        </button>
        <button
          @click="loginType = 'apikey'"
          class="px-6 py-2 rounded-r-lg transition-colors"
          :class="
            loginType === 'apikey'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 hover:bg-gray-300'
          "
        >
          API-Key Login
        </button>
      </div>

      <!-- tab内容 -->
      <div v-show="loginType == 'apikey'" class="w-80">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <div class="mb-4">
            <label class="block text-gray-700 mb-2">API Key</label>
            <input
              v-model="apiKey"
              type="password"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="please input your API Key"
            />
          </div>
          <button
            @click="handleApiKeyLogin"
            class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors"
          >
            Login
          </button>
        </div>
      </div>
      <div v-show="loginType == 'google'" class="w-80">
        <div id="google-signin-button" class="mb-4"></div>
        <button
          @click="handleGoogleLogin"
          class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors"
        >
          Login with Google
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { invoke } from "@tauri-apps/api/core";

const app = getCurrentInstance();
const showStatus = (message, type) => {
  app?.appContext.config.globalProperties.$showStatus(message, type);
};

const googleClientLoaded = ref(false);
// type LoginType = "apikey" | "google";
const loginType = ref("apikey");
const apiKey = ref("");
const user = ref(null);

// 处理重定向回调中的Google登录
const handleGoogleRedirect = async () => {
  try {
    // 检查URL中是否有Google登录的凭证参数
    const urlParams = new URLSearchParams(window.location.hash.substring(1));
    const credential = urlParams.get('id_token');

    if (credential) {
      console.log("检测到重定向回调，id_token:", credential.substring(0, 10) + "...");
      // 清除URL中的参数，避免刷新页面时重复处理
      window.history.replaceState({}, document.title, window.location.pathname);

      const userInfo = await invoke("login", {
        credentials: credential,
        from: "google"
      });

      user.value = userInfo;
      showStatus("login success", "success");
    } else {
      // 检查URL参数中是否有错误信息
      const searchParams = new URLSearchParams(window.location.search);
      const error = searchParams.get('error');
      if (error) {
        console.error("Google登录错误:", error);
        showStatus(`Google login fail: ${error}`, "error");
        // 清除错误参数
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }
  } catch (error) {
    console.error("Google redirect login error:", error);
    showStatus("login fail: " + error.message, "error");
  }
};

// 初始化 Google Sign-In
const initializeGoogleSignIn = () => {
  try {
    console.log("开始初始化 Google Sign-In");

    window.google.accounts.id.initialize({
      client_id:
        "************-qra10t4c44g03jbmsnh409iq353snkhu.apps.googleusercontent.com",
      callback: async (response) => {
        console.log("Google 登录回调触发", response);
        try {
          if (!response?.credential) {
            throw new Error("No credential received from Google");
          }

          console.log("调用 Tauri login 命令");
          const userInfo = await invoke("login", {
            credentials: response.credential,
            from: "google"
          });

          console.log("登录成功", userInfo);
          user.value = userInfo;
          showStatus("login success", "success");
        } catch (error) {
          console.error("Login error:", error);
          showStatus("login fail: " + error.message, "error");
        }
      },
      auto_select: false,
      cancel_on_tap_outside: true,
      use_fedcm_for_prompt: false,
    });

    // 立即渲染登录按钮
    const buttonElement = document.getElementById("google-signin-button");
    if (buttonElement) {
      console.log("渲染 Google 登录按钮");
      window.google.accounts.id.renderButton(buttonElement, {
        type: "standard",
        theme: "outline",
        size: "large",
        text: "signin_with",
        shape: "rectangular",
        logo_alignment: "left",
      });
    } else {
      console.error("找不到 google-signin-button 元素");
    }

    googleClientLoaded.value = true;
    console.log("Google Sign-In 初始化完成");
  } catch (error) {
    console.error("Google Sign-In 初始化失败:", error);
    showStatus("Google Sign-In initialization failed: " + error.message, "error");
  }
};

// 处理 Google 登录按钮点击
const handleGoogleLogin = () => {
  if (!googleClientLoaded.value) {
    showStatus("Google login server not ready, please wait a minute", "error");
    return;
  }

  // 触发 Google One Tap 登录
  window.google.accounts.id.prompt((notification) => {
    if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
      console.log("Google One Tap not displayed, showing manual login");
      // 如果 One Tap 不可用，可以考虑其他登录方式
      showStatus("Please use the Google Sign-In button above", "info");
    }
  });
};

const handleApiKeyLogin = async () => {
  if (!apiKey.value) {
    showStatus("please input API Key", "error");
    return;
  }

  try {
    const userInfo = await invoke("check_apikey", {
      apikey: apiKey.value
    });

    user.value = userInfo;
    showStatus("login success", "success");
  } catch (error) {
    console.error("API Key login fail:", error);
    showStatus(`login fail: ${error.message}`, "error");
  }
};

onMounted(async () => {
  console.log("Login组件加载，当前URL:", window.location.href);

  // 首先处理可能的重定向回调
  handleGoogleRedirect();

  // 检查是否已经加载了 Google OAuth 脚本
  if (window.google && window.google.accounts) {
    console.log("Google OAuth客户端已存在，直接初始化");
    initializeGoogleSignIn();
    return;
  }

  // 加载 Google OAuth
  const script = document.createElement("script");
  script.src = "https://accounts.google.com/gsi/client";
  script.async = true;
  script.defer = true;
  script.onload = () => {
    console.log("Google OAuth客户端脚本加载完成");
    // 等待一小段时间确保脚本完全初始化
    setTimeout(() => {
      initializeGoogleSignIn();
    }, 100);
  };
  script.onerror = (error) => {
    console.error("Google OAuth客户端脚本加载失败:", error);
    showStatus("Google login service load failed", "error");
  };
  document.head.appendChild(script);
});
</script>
