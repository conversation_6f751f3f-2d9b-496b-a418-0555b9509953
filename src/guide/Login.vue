<template>
  <div class="relative w-screen h-screen">
    <!-- 全屏遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-10"></div>

    <!-- 登录内容 -->
    <div
      class="relative flex flex-col items-center justify-center w-full h-full z-20"
    >
      <!-- tab标题 -->
      <div class="flex mb-8">
        <button
          @click="loginType = 'google'"
          class="px-6 py-2 rounded-l-lg transition-colors"
          :class="
            loginType === 'google'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 hover:bg-gray-300'
          "
        >
          Google Login
        </button>
        <button
          @click="loginType = 'apikey'"
          class="px-6 py-2 rounded-r-lg transition-colors"
          :class="
            loginType === 'apikey'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 hover:bg-gray-300'
          "
        >
          API-Key Login
        </button>
      </div>

      <!-- tab内容 -->
      <div v-show="loginType == 'apikey'" class="w-80">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <div class="mb-4">
            <label class="block text-gray-700 mb-2">API Key</label>
            <input
              v-model="apiKey"
              type="password"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="please input your API Key"
            />
          </div>
          <button
            @click="handleApiKeyLogin"
            class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors"
          >
            Login
          </button>
        </div>
      </div>
      <div v-show="loginType == 'google'" class="w-80">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <div class="mb-4 text-center">
            <p class="text-gray-600 mb-4">Click the button below to login with Google</p>
          </div>
          <button
            @click="handleGoogleLogin"
            class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors flex items-center justify-center"
          >
            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Login with Google
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { invoke } from "@tauri-apps/api/core";

const app = getCurrentInstance();
const showStatus = (message, type) => {
  app?.appContext.config.globalProperties.$showStatus(message, type);
};

const googleClientLoaded = ref(false);
// type LoginType = "apikey" | "google";
const loginType = ref("apikey");
const apiKey = ref("");
const user = ref(null);

// 处理重定向回调中的Google登录
const handleGoogleRedirect = async () => {
  try {
    // 检查URL中是否有Google登录的授权码参数
    const searchParams = new URLSearchParams(window.location.search);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    if (error) {
      console.error("Google登录错误:", error);
      showStatus(`Google login fail: ${error}`, "error");
      // 清除错误参数
      window.history.replaceState({}, document.title, window.location.pathname);
      return;
    }

    if (code && state) {
      console.log("检测到重定向回调，authorization code:", code.substring(0, 10) + "...");
      // 清除URL中的参数，避免刷新页面时重复处理
      window.history.replaceState({}, document.title, window.location.pathname);

      // 使用授权码进行登录
      showStatus("Processing login...", "info");
      const userInfo = await invoke("login", {
        credentials: code,
        from: "google_oauth"
      });

      user.value = userInfo;
      showStatus("login success", "success");
    }
  } catch (error) {
    console.error("Google redirect login error:", error);
    showStatus("login fail: " + error.message, "error");
  }
};

// 初始化 Google Sign-In - 使用适合 Tauri 的方式
const initializeGoogleSignIn = () => {
  try {
    console.log("开始初始化 Google Sign-In for Tauri");

    // 对于 Tauri 应用，我们不使用 Google 的 JavaScript SDK
    // 而是直接使用 OAuth 2.0 授权码流程
    googleClientLoaded.value = true;
    console.log("Google Sign-In 初始化完成 (Tauri mode)");
  } catch (error) {
    console.error("Google Sign-In 初始化失败:", error);
    showStatus("Google Sign-In initialization failed: " + error.message, "error");
  }
};

// 处理 Google 登录按钮点击
const handleGoogleLogin = async () => {
  if (!googleClientLoaded.value) {
    showStatus("Google login server not ready, please wait a minute", "error");
    return;
  }

  try {
    // 使用 Tauri 的 shell 插件在系统浏览器中打开 OAuth URL
    const { open } = await import("@tauri-apps/plugin-opener");

    // 创建 OAuth 2.0 授权 URL
    const clientId = "************-qra10t4c44g03jbmsnh409iq353snkhu.apps.googleusercontent.com";
    const redirectUri = encodeURIComponent("http://localhost:1420");
    const scope = encodeURIComponent("email profile openid");
    const state = Date.now().toString(); // 用于防止 CSRF 攻击

    const oauthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `response_type=code&` +
      `client_id=${clientId}&` +
      `redirect_uri=${redirectUri}&` +
      `scope=${scope}&` +
      `state=${state}&` +
      `access_type=offline&` +
      `prompt=consent`;

    console.log("Opening OAuth URL in system browser:", oauthUrl);
    await open(oauthUrl);

    showStatus("Please complete login in your browser, then return to this app", "info");
  } catch (error) {
    console.error("Failed to open OAuth URL:", error);
    showStatus("Failed to open login page: " + error.message, "error");
  }
};

const handleApiKeyLogin = async () => {
  if (!apiKey.value) {
    showStatus("please input API Key", "error");
    return;
  }

  try {
    const userInfo = await invoke("check_apikey", {
      apikey: apiKey.value
    });

    user.value = userInfo;
    showStatus("login success", "success");
  } catch (error) {
    console.error("API Key login fail:", error);
    showStatus(`login fail: ${error.message}`, "error");
  }
};

onMounted(async () => {
  console.log("Login组件加载，当前URL:", window.location.href);

  // 首先处理可能的重定向回调
  handleGoogleRedirect();

  // 初始化 Google Sign-In (Tauri 模式)
  initializeGoogleSignIn();
});
</script>
