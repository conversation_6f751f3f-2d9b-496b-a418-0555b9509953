<template>
  <div class="p-5">
    <h2 class="text-xl font-bold mb-4">Folders Setting</h2>
    <div class="text-2xl mb-4">
      you can choose folders that contains your pictures
    </div>
    <div class="mt-4 space-y-2">
      <div
        v-for="(folder, index) in selectedFolders"
        :key="index"
        class="flex items-center p-2 bg-gray-100 rounded"
      >
        <span class="flex-grow">{{ folder }}</span>
        <button
          @click="removeFolder(index)"
          class="ml-2 bg-red-500 text-white rounded px-1.5 py-0.5"
        >×</button>
        <div v-if="folderWarnings[index]" class="ml-2 text-yellow-500">
          {{ folderWarnings[index] }}
        </div>
      </div>
      <button
        @click="openFolderDialog"
        class="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
      >
        + add folder
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, ref } from "vue";
import { open } from "@tauri-apps/plugin-dialog";
import { NotificationType } from "@/types/notification";

// const app = getCurrentInstance();
// const showStatus = (message: string, type: NotificationType) => {
//   app?.appContext.config.globalProperties.$showStatus(message, type);
// };

const selectedFolders = ref<string[]>([]);
const folderWarnings = ref<Record<number, string>>({});

const openFolderDialog = async () => {
  const selected = await open({
    directory: true,
    multiple: false,
    title: "choose folder",
  });

  if (selected) {
    const newFolder = selected;
    const warning = checkFolderRelationships(newFolder);
    selectedFolders.value.push(newFolder);
    if (warning) {
      folderWarnings.value[selectedFolders.value.length - 1] = warning;
    }
  }
};

const removeFolder = (index: number) => {
  selectedFolders.value.splice(index, 1);
  delete folderWarnings.value[index];
};

const checkFolderRelationships = (newFolder: string): string | null => {
  for (let i = 0; i < selectedFolders.value.length; i++) {
    const existingFolder = selectedFolders.value[i];
    if (newFolder.startsWith(existingFolder + "\\")) {
      return "warning: This folder is a subdirectory of the selected folder";
    }
    if (existingFolder.startsWith(newFolder + "\\")) {
      return "warning: The selected folder contains this folder";
    }
  }
  return null;
};
</script>

