<template>
    <div >
        <!-- 搜索和上传区域 -->
        <div class="bg-white rounded-2xl shadow-sm p-6 mb-6">
          <div
            class="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0"
          >
            <div class="relative w-full md:w-1/2">
              <input
                type="text"
                v-model="searchQuery"
                @input="handleSearch"
                placeholder="搜索图片名称..."
                class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span
                v-if="searchQuery"
                @click="clearSearch"
                class="absolute right-3 top-3 cursor-pointer text-gray-500 hover:text-gray-700"
                >×</span
              >
            </div>

            <div class="flex space-x-3">
              <button
                @click="openFileDialog"
                class="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-xl hover:from-blue-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm hover:shadow"
              >
                上传图片
              </button>
              <input
                type="file"
                ref="fileInput"
                @change="handleFileUpload"
                accept="image/*"
                multiple
                class="hidden"
              />
            </div>
          </div>
        </div>

        <!-- 状态消息 -->
        <div
          v-if="statusMessage"
          class="mb-6 p-4 rounded-xl shadow-sm transition-all duration-300"
          :class="
            statusType === 'error'
              ? 'bg-red-50 text-red-700 border border-red-100'
              : 'bg-green-50 text-green-700 border border-green-100'
          "
        >
          {{ statusMessage }}
        </div>

        <!-- 加载动画 -->
        <div v-if="loading" class="flex justify-center my-12">
          <div
            class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
          ></div>
        </div>

        <!-- 空状态 -->
        <div
          v-else-if="images.length === 0"
          class="bg-white rounded-2xl shadow-sm p-12 text-center"
        >
          <div class="text-gray-400 mb-4">
            <svg
              class="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p class="text-lg">未找到图片，请上传新图片</p>
          </div>
        </div>

        <!-- 图片网格 -->
        <div
          v-else
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
        >
          <div
            v-for="image in images"
            :key="image.id"
            class="group relative bg-white rounded-2xl shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md"
          >
            <img
              :src="convertFilePath(image.path)"
              :alt="image.name"
              @click="openImagePreview(image)"
              class="w-full h-48 object-cover cursor-pointer transition-transform duration-300 group-hover:scale-105"
            />
            <div
              class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent text-white p-3"
            >
              <p class="truncate text-sm">{{ image.name }}</p>
            </div>
            <button
              @click="deleteImage(image.id)"
              class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-red-600"
            >
              ×
            </button>
          </div>
        </div>

        <!-- 图片预览弹出层 -->
        <div
          v-if="previewImage"
          class="fixed top-0 right-0 bottom-0 w-1/2 bg-black/90 z-50 backdrop-blur-sm overflow-y-auto"
          @click="closeImagePreview"
        >
          <div class="p-6 h-full flex flex-col">
            <div class="flex-1 flex items-center justify-center">
              <img
                :src="convertFilePath(previewImage.path)"
                :alt="previewImage.name"
                class="max-w-full max-h-full object-contain rounded-lg"
              />
            </div>
            <div class="mt-4 text-white text-center">
              <p class="text-lg">{{ previewImage.name }}</p>
            </div>
          </div>
        </div>
      </div>
</template>


<script setup>
import { ref, onMounted } from "vue";
import { invoke } from "@tauri-apps/api/core";
import { open, confirm } from "@tauri-apps/plugin-dialog";
import { convertFileSrc } from "@tauri-apps/api/core";

const images = ref([]);
const loading = ref(true);
const searchQuery = ref("");
const statusMessage = ref("");
const statusType = ref("success");
const fileInput = ref(null);
const previewImage = ref(null);
const user = ref(null);

// 获取所有图片
const fetchImages = async () => {
  loading.value = true;
  try {
    images.value = await invoke("get_images");
    // 按创建时间排序，最新的在前面
    images.value.sort((a, b) => b.created_at - a.created_at);
  } catch (error) {
    showStatus(`获取图片失败: ${error}`, "error");
  } finally {
    loading.value = false;
  }
};

// 搜索图片
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    await fetchImages();
    return;
  }

  loading.value = true;
  try {
    images.value = await invoke("search_images", { query: searchQuery.value });
    images.value.sort((a, b) => b.created_at - a.created_at);
  } catch (error) {
    showStatus(`搜索失败: ${error}`, "error");
  } finally {
    loading.value = false;
  }
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = "";
  fetchImages();
};

// 打开文件选择对话框
const openFileDialog = async () => {
  try {
    const selected = await open({
      multiple: true,
      filters: [
        {
          name: "Images",
          extensions: ["jpg", "jpeg", "png", "gif", "webp", "bmp"],
        },
      ],
    });

    if (selected && selected.length > 0) {
      await uploadFiles(selected);
    }
  } catch (error) {
    showStatus(`选择文件失败: ${error}`, "error");
  }
};

// 直接通过input处理上传
const handleFileUpload = async (event) => {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  const filePaths = Array.from(files).map((file) => file.path);
  await uploadFiles(filePaths);

  // 重置input，使得相同文件可以再次选择
  event.target.value = "";
};

// 上传文件处理
const uploadFiles = async (filePaths) => {
  loading.value = true;
  let successCount = 0;
  let errorCount = 0;

  for (const filePath of filePaths) {
    try {
      await invoke("upload_image", { filePath });
      successCount++;
    } catch (error) {
      errorCount++;
      console.error(`上传文件失败: ${filePath}`, error);
    }
  }

  // 刷新图片列表
  await fetchImages();

  // 显示上传结果
  if (errorCount === 0) {
    showStatus(`成功上传 ${successCount} 个图片`, "success");
  } else {
    showStatus(
      `上传完成: ${successCount} 成功, ${errorCount} 失败`,
      errorCount > 0 ? "error" : "success"
    );
  }

  loading.value = false;
};

// 删除图片
const deleteImage = async (id) => {
  if (!(await confirm("确定要删除这张图片吗？"))) return;

  loading.value = true;
  try {
    await invoke("delete_image", { id });
    showStatus("图片已删除", "success");
    await fetchImages();
  } catch (error) {
    showStatus(`删除失败: ${error}`, "error");
  } finally {
    loading.value = false;
  }
};

// 打开图片预览
const openImagePreview = (image) => {
  previewImage.value = image;
};

// 关闭图片预览
const closeImagePreview = () => {
  previewImage.value = null;
};

// 转换文件路径为可访问URL
const convertFilePath = (path) => {
  return convertFileSrc(path);
};

// 显示状态消息
const showStatus = (message, type = "success") => {
  statusMessage.value = message;
  statusType.value = type;

  // 3秒后自动清除消息
  setTimeout(() => {
    statusMessage.value = "";
  }, 3000);
};

// 获取当前用户状态
// const getCurrentUser = async () => {
//   try {
//     const currentUser = await invoke("get_current_user");
//     if (currentUser) {
//       user.value = currentUser;
//     }
//   } catch (error) {
//     console.error("获取用户信息失败:", error);
//   }
// };

</script>
