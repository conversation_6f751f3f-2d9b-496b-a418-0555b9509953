<template>
  <div
    v-if="show"
    class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg transition-all duration-300 z-50"
    :class="{
      'bg-green-500 text-white': type === 'success',
      'bg-yellow-500 text-black': type === 'warn',
      'bg-red-500 text-white': type === 'error',
    }"
  >
    {{ message }}
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const show = ref(false);
const message = ref("");
const type = ref("");

let timeout: number | null = null;
const showNotification = (
  msg: string,
  notificationType: "success" | "warn" | "error"
) => {
  message.value = msg;
  type.value = notificationType;
  show.value = true;

  if (timeout) {
    clearTimeout(timeout);
  }

  timeout = setTimeout(() => {
    show.value = false;
    timeout = null;
  }, 3000);
};

defineExpose({
  showNotification,
});
</script>
