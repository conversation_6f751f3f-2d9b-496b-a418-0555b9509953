<template>
  <nav class="w-64 bg-white shadow-sm flex flex-col h-screen sticky top-0">
    <div class="p-4 border-b border-gray-200">
      <h1
        class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
      >
        Image Search
      </h1>
    </div>

    <!-- 用户信息区域 -->
    <div class="p-4 mt-auto border-t border-gray-200">
      <div class="flex items-center space-x-4">
        <template v-if="user">
          <img
            :src="user.avatar_url"
            :alt="user.name"
            class="w-10 h-10 rounded-full shadow-lg"
          />
          <span class="text-gray-700">{{ user.name }}</span>
        </template>
        <div v-else id="google-signin-button"></div>
      </div>
    </div>
  </nav>
</template>

<script setup>


</script>