import { defineStore } from "pinia"

export interface UserState {
  logined: boolean
  picture: string
  username: string
  email: string
}

export const useUserStore = defineStore('user', {
  state: () => ( { logined: false, picture: '', username: '', email: '' } as UserState ),
  actions: {
    setUser(u: UserState) {
      this.logined = true
      this.picture = u.picture
      this.username = u.username
      this.email = u.email
      return this
    },
  },
})
