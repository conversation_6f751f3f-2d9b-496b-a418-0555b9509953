import { invoke } from "@tauri-apps/api/core";
import { defineStore } from "pinia";

export enum GuideStep {
  Login = "Login",
  Setting = "Setting"
}

export interface GuideState {
  finished: boolean;
  step: GuideStep;
}

export const useGuideStore = defineStore("guide", {
  state: () => ({ finished: false, step: "Login" } as GuideState),
  actions: {
    setGuide(u: GuideState) {
      this.finished = u.finished;
      this.step = u.step;
      return this;
    },
    next() {
      if (this.step === "Login") {
        this.step = GuideStep.Setting;
      } else {
        this.finished = true;
      }
      invoke("save_guide", { finished: this.finished, step: this.step });
    },
  },
});
