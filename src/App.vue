<template>
  <div class="min-h-screen bg-gray-50 flex">
    <Guide v-if="!guideFinished" />

    <template v-if="guideFinished">
      <Header />
      <!-- 主内容区域 -->
      <div class="flex-1 overflow-auto">
        <div class="container mx-auto px-4 py-6 max-w-6xl">
          <search v-if="tab === 'search'" />
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import Header from "./components/Header.vue";
import Search from "./components/Search.vue";
import Guide from "./guide/Guide.vue";
import { onMounted, ref } from "vue";
import { GuideState, useGuideStore } from "./stores/guide";
import { UserState, useUserStore } from "./stores/user";
import { listen } from "@tauri-apps/api/event";

type Tab = "search" | "upload" | "settings";
const tab = ref<Tab>("search");

const guideFinished = ref<boolean>(false);

onMounted(async () => {
  listen<GuideState>("guide_loaded", (event) => {
    const guide = event.payload;
    guideFinished.value = guide.finished;
    useGuideStore().setGuide(guide);
  });

  listen<UserState>("user_loaded", (event) => {
    const user = event.payload;
    useUserStore().setUser(user);
  });
});
</script>
