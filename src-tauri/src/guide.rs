use std::path::Path;

use serde::{Deserialize, Serialize};
use tauri::State;

use crate::{error::AppError, utils::localfile, AppState};

#[derive(Serialize, Deserialize, Clone)]
pub enum GuideStep {
    Login,
    Setting,
}
#[derive(Serialize, Deserialize, Clone)]
pub struct Guide {
    pub finished: bool,
    pub step: GuideStep,
}

const GUIDE_INFO_PATH: &str = "guide_info.json";

#[tauri::command]
pub async fn save_guide(params: Guide, state: State<'_, AppState>) -> Result<(), AppError> {
    let path = Path::join(state.datadir.as_path(), GUIDE_INFO_PATH);
    localfile::save_local(
        path.to_string_lossy().to_string(),
        serde_json::to_vec(&params)?,
    )?;
    Ok(())
}

pub fn load_guide(datadir: &std::path::Path) -> Option<Guide> {
    let path: std::path::PathBuf = Path::join(datadir, GUIDE_INFO_PATH);
    let data = localfile::load_local(path.to_string_lossy().to_string());

    if let Ok(data) = data {
        return if data.is_empty() {
            None
        } else {
            let data = serde_json::from_slice(&data);
            if let Ok(data) = data {
                return Some(data);
            } else {
                return None;
            }
        };
    } else {
        return None;
    }
}
