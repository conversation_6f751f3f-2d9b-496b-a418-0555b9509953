use std::{collections::HashMap, path::Path};

use serde::{Deserialize, Serialize};
use tauri::State;
use tauri_plugin_http::reqwest::{self, Client};

use crate::{error::AppError, utils::localfile, AppState};

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct UserInfo {
    pub username: String,
    pub email: String,
    pub picture: Option<String>,
}

#[tauri::command]
pub fn get_current_user(state: State<AppState>) -> Result<Option<UserInfo>, AppError> {
    let u = state.user.read()?;

    Ok(u.clone())
}

#[derive(Serialize, Deserialize, Debug)]
pub struct LoginModel {
    credentials: String,
    from: String,
}

#[derive(Serialize, Deserialize)]
pub struct LoginResp {
    token: String,
    user: Claims,
}
#[derive(Serialize, Deserialize)]
pub struct Claims {
    pub uid: i32,
    pub username: String,
    pub email: String,
    pub picture: Option<String>,
    // 秒
    pub exp: i64,
}

impl Into<UserInfo> for Claims {
    fn into(self) -> UserInfo {
        UserInfo {
            username: self.username,
            email: self.email,
            picture: self.picture,
        }
    }
}

const LOGIN_INFO_PATH: &str = "login_info.json";

pub fn load_user(datadir: &std::path::Path) -> Option<(String, UserInfo)> {
    let path: std::path::PathBuf = Path::join(datadir, LOGIN_INFO_PATH);

    if let Ok(content) = localfile::load_local(path.to_string_lossy().to_string()) {
        if let Ok(login_info) = serde_json::from_slice::<LoginResp>(&content) {
            Some((login_info.token, login_info.user.into()))
        } else {
            None
        }
    } else {
        None
    }
}

async fn after_check(r: LoginResp, state: State<'_, AppState>) -> Result<UserInfo, AppError> {
    let path = Path::join(state.datadir.as_path(), LOGIN_INFO_PATH);
    localfile::save_local(path.to_string_lossy().to_string(), serde_json::to_vec(&r)?)?;

    let user: UserInfo = r.user.into();

    state.user.write()?.replace(user.clone());
    state.token.write()?.replace(r.token);
    Ok(user)
}

/**
 * 登录验证
 */
#[tauri::command]
pub async fn login(params: LoginModel, state: State<'_, AppState>) -> Result<UserInfo, AppError> {
    println!("Login command called with params: {:?}", params);

    // 根据登录类型选择不同的处理方式
    let url = if params.from == "google_oauth" {
        // OAuth 授权码流程
        format!(
            "https://imgsearch.dev/api/login/v1/oauth?code={}&reg_from={}",
            &params.credentials, &params.from
        )
    } else {
        // 原有的 ID token 流程
        format!(
            "https://imgsearch.dev/api/login/v1?token={}&reg_from={}",
            &params.credentials, &params.from
        )
    };

    println!("Making request to: {}", url);

    let r = reqwest::get(&url).await;

    if let Err(e) = r {
        println!("Request failed: {:?}", e);
        return Err(AppError::AuthError(e.to_string()));
    }
    let r = r.unwrap();

    let status = r.status();
    println!("Response status: {}", status);

    if !status.is_success() {
        let error_text = r.text().await.unwrap_or_else(|_| "Unknown error".to_string());
        println!("Error response: {}", error_text);

        // 如果是 OAuth 流程且服务器返回 404，可能是服务器还不支持 OAuth 端点
        if params.from == "google_oauth" && status == 404 {
            return Err(AppError::AuthError("OAuth login not supported by server yet. Please use API key login or contact support.".to_string()));
        }

        return Err(AppError::AuthError(format!("Server error: {}", error_text)));
    }

    let r = r.json::<LoginResp>().await?;
    println!("Login response received successfully");

    after_check(r, state.clone()).await
}

#[derive(Serialize, Deserialize)]
pub struct CheckModel {
    pub apikey: String,
}

/**
 * 直接使用apikey登录
 * sk-eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************.hEGwGeA71cXpcwJyQZYU9Ik9xuPQf67biIasaDARiIk
 */
#[tauri::command]
pub async fn check_apikey(
    params: CheckModel,
    state: State<'_, AppState>,
) -> Result<UserInfo, AppError> {
    let mut headers = HashMap::with_capacity(2);
    headers.insert(
        "Authorization".to_string(),
        format!("Bearer {}", &params.apikey),
    );
    headers.insert("content-type".to_string(), "application/json".to_string());

    let c = Client::builder()
        .default_headers((&headers).try_into().expect("valid headers"))
        .build()?;

    let r = c
        .get(format!("https://imgsearch.dev/api/check_apikey/v1"))
        .send()
        .await;

    if let Err(e) = r {
        return Err(AppError::AuthError(e.to_string()));
    }
    let r = r.unwrap();

    let r = r.json::<LoginResp>().await?;
    after_check(r, state).await
}
