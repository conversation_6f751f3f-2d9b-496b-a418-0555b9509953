use std::{fs::File, io::{Read, Write}};

use crate::error::AppError;


pub fn save_local(path: String, data: Vec<u8>) -> Result<(), AppError> {

    let mut file = File::create(path)?;
    file.write_all(&data)?;

    Ok(())
}

pub fn load_local(path: String) -> Result<Vec<u8>, AppError> {

    let mut file = File::open(path)?;
    let mut data = Vec::new();
    file.read_to_end(&mut data)?;

    Ok(data)
}