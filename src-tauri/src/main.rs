// src-tauri/src/main.rs
#![cfg_attr(
    all(not(debug_assertions), target_os = "windows"),
    windows_subsystem = "windows"
)]
// mod image;
mod error;
mod guide;
mod user;
mod utils;

use std::{path::PathBuf, sync::RwLock};
use tauri::{Emitter, Manager}; // 添加这一行导入 Manager trait
use user::UserInfo;

pub struct AppState {
    datadir: PathBuf,
    token: RwLock<Option<String>>,
    user: RwLock<Option<UserInfo>>, // 新增用户状态
}

fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            let app_dir = app
                .path()
                .app_data_dir()
                .expect("Failed to get app data directory");

            if let Some(g) = guide::load_guide(app_dir.as_path()) {
                app.handle().emit("guide_loaded", g)?;
            }

            let mut token = None;
            let mut user = None;

            if let Some( (_token, _user)) = user::load_user(app_dir.as_path()) {
                token = Some(_token);
                user = Some(_user.clone());
                app.handle().emit("user_loaded", _user)?;
            }

            let app_state = AppState {
                datadir: app_dir,
                token: RwLock::new(token),
                user: RwLock::new(user), // 初始化用户状态
            };
            app.manage(app_state);

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // image::search_images,
            // image::upload_image,
            // image::delete_image,
            user::get_current_user,
            user::login,
            user::check_apikey,
            guide::save_guide,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
