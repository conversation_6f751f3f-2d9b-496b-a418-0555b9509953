{"$schema": "https://schema.tauri.app/config/2", "productName": "imgsearch-app", "version": "0.1.0", "identifier": "dev.imgsearch.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "imgsearch-app", "width": 800, "height": 600}], "security": {"csp": "default-src 'self'; connect-src 'self' https://accounts.google.com https://www.googleapis.com https://imgsearch.dev; script-src 'self' 'unsafe-inline' https://accounts.google.com; frame-src 'self' https://accounts.google.com; img-src 'self' https: data:; form-action 'self' https://accounts.google.com; navigate-to 'self' https://accounts.google.com http://localhost:*;", "headers": {"Cross-Origin-Opener-Policy": "same-origin-allow-popups"}, "dangerousDisableAssetCspModification": false, "assetProtocol": {"enable": true, "scope": ["**"]}}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"fs": {"scope": {"allow": ["**"], "deny": []}}, "shell": {"open": true}}}