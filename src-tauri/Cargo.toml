[package]
name = "image-tauri"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

[profile.dev] 
debug=false
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["protocol-asset", "rustls-tls"] }
tauri-plugin-opener = "2"
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
tauri-plugin-shell = "2"
tauri-plugin-http = {version = "2",features = ["json"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
uuid = { version = "1.17.0", features = ["v4"] }
jsonwebtoken = "9"
base64 = "0.22.1"
thiserror = "2.0.12"
fast_image_resize = { version = "5.1.3", features = ["image"] }
image = { version = "0.25.6", features = ["jpeg", "png", "webp"] }
